#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/nearley@2.20.1/node_modules/nearley/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/nearley@2.20.1/node_modules/nearley/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/nearley@2.20.1/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/nearley@2.20.1/node_modules/nearley/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/nearley@2.20.1/node_modules/nearley/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/nearley@2.20.1/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nearley/bin/nearley-railroad.js" "$@"
else
  exec node  "$basedir/../nearley/bin/nearley-railroad.js" "$@"
fi
