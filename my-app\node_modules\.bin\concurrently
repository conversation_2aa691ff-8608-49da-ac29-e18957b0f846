#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules/concurrently/dist/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules/concurrently/dist/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules/concurrently/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules/concurrently/dist/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules/concurrently/dist/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules/concurrently/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/concurrently@8.2.2/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../concurrently/dist/bin/concurrently.js" "$@"
else
  exec node  "$basedir/../concurrently/dist/bin/concurrently.js" "$@"
fi
