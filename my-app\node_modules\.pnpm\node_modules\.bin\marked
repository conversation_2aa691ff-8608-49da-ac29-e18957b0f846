#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/marked@13.0.3/node_modules/marked/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/marked@13.0.3/node_modules/marked/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/marked@13.0.3/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/marked@13.0.3/node_modules/marked/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/marked@13.0.3/node_modules/marked/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/marked@13.0.3/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../marked/bin/marked.js" "$@"
else
  exec node  "$basedir/../marked/bin/marked.js" "$@"
fi
