#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules/firebase-tools/lib/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules/firebase-tools/lib/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules/firebase-tools/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules/firebase-tools/lib/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules/firebase-tools/lib/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules/firebase-tools/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/firebase-tools@13.35.1_encoding@0.1.13/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../firebase-tools/lib/bin/firebase.js" "$@"
else
  exec node  "$basedir/../firebase-tools/lib/bin/firebase.js" "$@"
fi
