# System Architecture Overview

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React UI<br/>Port 5173]
        PUB[Public Pages]
        ADMIN[Admin Panel]
        AUTH_UI[Auth Components]
    end
    
    subgraph "API Layer"
        API[Hono API<br/>Port 8787]
        ROUTES[Route Handlers]
        MIDDLEWARE[Auth Middleware]
        VALIDATION[Input Validation]
    end
    
    subgraph "Data Layer"
        DB[(PostgreSQL<br/>Port 5433)]
        SCHEMA[Drizzle Schema]
        MIGRATIONS[DB Migrations]
    end
    
    subgraph "External Services"
        FIREBASE[Firebase Auth]
        MAPBOX[Mapbox GL JS]
        CLOUDFLARE[Cloudflare Workers]
    end
    
    UI --> API
    API --> DB
    UI --> FIREBASE
    API --> FIREBASE
    UI --> MAPBOX
    API --> CLOUDFLARE
```

## 🎯 Core Components

### Frontend (React 19 + TypeScript)
- **Location**: `ui/` package
- **Purpose**: User interface for public directory and admin management
- **Key Features**:
  - Public business directory browsing
  - Business application submission
  - Admin panel for content management
  - Responsive design with Tailwind CSS
  - Dark/light theme support

### Backend (Hono API)
- **Location**: `server/` package  
- **Purpose**: RESTful API with authentication and business logic
- **Key Features**:
  - Firebase Auth integration
  - Role-based access control
  - Business CRUD operations
  - Review and rating system
  - Admin approval workflows

### Database (PostgreSQL + Drizzle)
- **Location**: `server/src/schema/`
- **Purpose**: Persistent data storage with type-safe ORM
- **Key Tables**:
  - `users` - User accounts and admin roles
  - `businesses` - Business listings and details
  - `categories` - Business categorization
  - `reviews` - User reviews and ratings
  - `applications` - Business application submissions

## 🔐 Security Architecture

### Authentication Flow
1. **Frontend**: Firebase Auth SDK handles user login
2. **Token Verification**: Hono middleware validates Firebase JWT
3. **Role Authorization**: Admin routes check `is_admin` flag
4. **API Protection**: All admin endpoints require valid auth

### Data Protection
- Environment variables for sensitive configuration
- Firebase Admin SDK for server-side auth verification
- Input validation on all API endpoints
- SQL injection protection via Drizzle ORM

## 🚀 Deployment Architecture

### Local Development
- **UI**: Vite dev server (port 5173)
- **API**: Node.js server (port 8787)
- **Database**: Embedded PostgreSQL (port 5433)
- **Auth**: Firebase emulator

### Production (Cloudflare)
- **UI**: Cloudflare Pages
- **API**: Cloudflare Workers
- **Database**: Neon PostgreSQL
- **Auth**: Firebase production

## 📊 Data Flow Patterns

### Public User Journey
```
User Request → React Router → Component → API Call → Hono Route → Database Query → Response
```

### Admin Operations
```
Admin Action → Auth Check → Role Validation → API Call → Business Logic → Database Update → UI Update
```

### Business Application Flow
```
Public Form → Validation → Database Insert → Admin Notification → Review Process → Approval/Rejection
```

## 🔧 Technology Decisions

| Decision | Rationale | Trade-offs |
|----------|-----------|------------|
| React 19 | Latest features, compiler optimizations | Bleeding edge, potential instability |
| Hono | Lightweight, fast, Cloudflare-optimized | Smaller ecosystem vs Express |
| Drizzle ORM | Type safety, performance, PostgreSQL focus | Learning curve vs Prisma |
| Firebase Auth | Managed service, Google integration | Vendor lock-in, cost scaling |
| Tailwind CSS | Utility-first, consistent design system | Large CSS bundle, learning curve |

---

**Created**: 2025-01-08  
**Last Updated**: 2025-01-08  
**Reviewer**: Code Archaeologist Agent
