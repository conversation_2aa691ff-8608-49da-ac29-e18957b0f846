#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules/superstatic/lib/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules/superstatic/lib/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules/superstatic/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules/superstatic/lib/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules/superstatic/lib/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules/superstatic/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/superstatic@9.2.0_encoding@0.1.13/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../superstatic/lib/bin/server.js" "$@"
else
  exec node  "$basedir/../superstatic/lib/bin/server.js" "$@"
fi
