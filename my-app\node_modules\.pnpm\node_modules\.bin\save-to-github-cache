#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/install-artifact-from-github@1.4.0/node_modules/install-artifact-from-github/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/install-artifact-from-github@1.4.0/node_modules/install-artifact-from-github/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/install-artifact-from-github@1.4.0/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/install-artifact-from-github@1.4.0/node_modules/install-artifact-from-github/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/install-artifact-from-github@1.4.0/node_modules/install-artifact-from-github/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/install-artifact-from-github@1.4.0/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../install-artifact-from-github/bin/save-to-github-cache.js" "$@"
else
  exec node  "$basedir/../install-artifact-from-github/bin/save-to-github-cache.js" "$@"
fi
