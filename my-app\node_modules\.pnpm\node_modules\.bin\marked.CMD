@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\marked@13.0.3\node_modules\marked\bin\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\marked@13.0.3\node_modules\marked\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\marked@13.0.3\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\marked@13.0.3\node_modules\marked\bin\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\marked@13.0.3\node_modules\marked\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\marked@13.0.3\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\marked\bin\marked.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\marked\bin\marked.js" %*
)
