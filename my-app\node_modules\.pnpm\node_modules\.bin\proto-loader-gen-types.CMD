@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\proto-loader\build\bin\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\proto-loader\build\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\proto-loader\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\proto-loader\build\bin\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\proto-loader\build\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\proto-loader\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules\@grpc\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\@grpc+proto-loader@0.7.15\node_modules;C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@grpc\proto-loader\build\bin\proto-loader-gen-types.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@grpc\proto-loader\build\bin\proto-loader-gen-types.js" %*
)
