#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/sql-formatter@15.6.2/node_modules/sql-formatter/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/sql-formatter@15.6.2/node_modules/sql-formatter/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/sql-formatter@15.6.2/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/sql-formatter@15.6.2/node_modules/sql-formatter/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/sql-formatter@15.6.2/node_modules/sql-formatter/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/sql-formatter@15.6.2/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../sql-formatter/bin/sql-formatter-cli.cjs" "$@"
else
  exec node  "$basedir/../sql-formatter/bin/sql-formatter-cli.cjs" "$@"
fi
