#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node-gyp@11.2.0/node_modules/node-gyp/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node-gyp@11.2.0/node_modules/node-gyp/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node-gyp@11.2.0/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node-gyp@11.2.0/node_modules/node-gyp/bin/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node-gyp@11.2.0/node_modules/node-gyp/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node-gyp@11.2.0/node_modules:/mnt/c/Users/<USER>/shop-bze/my-app/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../node-gyp/bin/node-gyp.js" "$@"
else
  exec node  "$basedir/../node-gyp/bin/node-gyp.js" "$@"
fi
